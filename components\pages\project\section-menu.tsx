"use client";

import React from "react";
import { Move, Settings, Copy } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import DeleteSection from "./section-menus/delete-section";
import { Section } from "@prisma/client";
import ExternalLink from "./section-menus/external-link";
import DragSection from "./section-menus/drag-section";

type Props = {
  section: Section;
};

const SectionMenu = ({ section }: Props) => {
  return (
    <div
      className={cn(
        "absolute top-4 right-4 opacity-0 group-hover/section:opacity-100",
        "transition-all duration-300 transform translate-x-2 group-hover/section:translate-x-0",
        "bg-background/95 backdrop-blur-sm border border-border rounded-lg p-1 shadow-lg",
        "flex items-center gap-1 z-30"
      )}
    >
      {/* Move/Drag handle */}
      <DragSection section={section} />

      {/* Settings */}

      {/* Duplicate */}
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted"
        title="Duplicate section"
      >
        <Copy className="h-4 w-4" />
      </Button>

      {/* External Link */}
      <ExternalLink section={section} />

      {/* Delete */}
      <DeleteSection section={section} />
    </div>
  );
};

export default SectionMenu;
